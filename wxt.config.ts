import { defineConfig } from 'wxt';

// See https://wxt.dev/api/config.html
console.log('wxt import.meta.env.MODE', import.meta.env.MODE);
console.log('cross NODE_ENV=development', process.env.NODE_ENV);

const isProduction = process.env.NODE_ENV === 'production';
const isDevelopment = process.env.NODE_ENV === 'development';

export default defineConfig({
  modules: ['@wxt-dev/module-react', '@wxt-dev/auto-icons', '@wxt-dev/webextension-polyfill'],
  vite: () => ({
    plugins: [],
    define: {
      // 确保环境变量在构建时可用
      __DEV__: JSON.stringify(isDevelopment),
      __PROD__: JSON.stringify(isProduction),
    },
  }),
  // @ts-ignore
  autoIcons: {
    // ...
  },
  manifest: {
    name: 'InHand Sales Assistant',
    description: 'InHand Sales Assistant: AI-driven insights & automation for Zoho CRM.',
    permissions: [],
    host_permissions: [
      '*://crm.zoho.com/*',
      '*://crm.zoho.com.cn/*',
      '*://one.zoho.com/*',
      // 开发环境添加本地主机权限
      ...(isDevelopment ? ['*://127.0.0.1/*', '*://localhost/*'] : []),
      '*://crm.zoho.com/*',
    ],
    default_locale: 'en',
    action: {
      default_title: 'InHand Sales Assistant',
      default_popup: 'popup/index.html',
    },
    background: {
      service_worker: 'background/background.ts',
    },
  },
  webExt: {
    disabled: true,
    openConsole: isDevelopment,
    openDevtools: isDevelopment,
  },
  imports: {
    eslintrc: {
      enabled: 9,
    },
  },
  outDir: 'dist',
});
