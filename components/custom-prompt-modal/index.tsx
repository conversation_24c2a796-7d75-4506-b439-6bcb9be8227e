import { Form, Input, Modal, ModalProps } from 'antd';
import React from 'react';

export interface CustomPromptsType {
  prompt: string;
  coverSystemPrompt: boolean;
}

type CustomPromptModalProps = {
  onConfirm: (data: CustomPromptsType) => void;
} & ModalProps;

const CustomPromptModal: React.FC<CustomPromptModalProps> = ({ open, onConfirm, onCancel }) => {
  const [form] = Form.useForm();
  return (
    <Form
      form={form}
      initialValues={{
        coverSystemPrompt: false,
      }}
      layout='vertical'
      onFinish={(values) => onConfirm?.(values)}
    >
      <Modal
        title='Custom AI Prompt'
        open={open}
        getContainer={false}
        onCancel={onCancel}
        okButtonProps={{
          htmlType: 'submit',
        }}
      >
        <Form.Item
          label='Your Custom Filter Strategies'
          name='prompt'
          required
          rules={[{ required: true, message: 'Please input your filter strategy' }]}
          // tooltip={{
          //   getPopupContainer: (node) => node.parentElement as HTMLElement,
          //   placement: 'top',
          //   title: <div>This is prompt demo</div>,
          // }}
        >
          <Input.TextArea
            placeholder='Please input your filter strategies, eg: Department, Business background, Title, Experience etc. '
            rows={8}
          />
        </Form.Item>
        {/*<Form.Item label='Only Use Your Prompt' name='coverSystemPrompt'>*/}
        {/*  <Radio.Group*/}
        {/*    options={[*/}
        {/*      {*/}
        {/*        label: 'Yes',*/}
        {/*        value: true,*/}
        {/*      },*/}
        {/*      {*/}
        {/*        label: 'No',*/}
        {/*        value: false,*/}
        {/*      },*/}
        {/*    ]}*/}
        {/*  />*/}
        {/*</Form.Item>*/}
      </Modal>
    </Form>
  );
};

export default CustomPromptModal;
