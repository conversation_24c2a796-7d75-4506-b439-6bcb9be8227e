import { DownOutlined } from '@ant-design/icons';
import { EventSourceMessage, fetchEventSource } from '@microsoft/fetch-event-source';
import { useInterval, useRequest, useUnmount } from 'ahooks';
import { Button, Dropdown, Space, Typography } from 'antd';
import { delay, last } from 'es-toolkit';
import React, { useCallback, useEffect, useRef, useState } from 'react';

import config from '@/config';
import { getAccountId } from '@/utils';
import { BASE_URL, X_API_KEY } from '@/utils/request';

import MagicSVG from '~/assets/magic.svg';

import CustomPromptModalModal, { CustomPromptsType } from '../custom-prompt-modal';

import { displayTextMap, validMessageTypes } from './constants';
import { EventStreamItem, ResultDataItem } from './data';
import { detectTaskStatus, startDetectTask } from './service';
import { formatMessage } from './utils.ts';

interface DetectContactsTypes {
  accountId?: string;
  accountName?: string;
  accountLocation?: string;
  accountWebsite?: string;
}

// 状态类型定义
type ProcessState = {
  current: EventStreamItem | null;
  isPolling: boolean;
  showMessage: boolean;
};

export const DetectContacts: React.FC<DetectContactsTypes> = () => {
  const accountId = getAccountId() as string;

  // 合并状态管理
  const [displayText, setDisplayText] = useState<string>(displayTextMap.detect_contacts);
  const [processState, setProcessState] = useState<ProcessState>({
    current: null,
    isPolling: true,
    showMessage: true,
  });
  const [customPromptModalOpen, setCustomPromptModalOpen] = useState<boolean>(false);

  // Refs for EventSource management
  const ctrlRef = useRef<AbortController | null>(null);
  const processRef = useRef<EventStreamItem | null>(null);

  const reloadPage = () => {
    window.location.reload();
  };

  // 统一的进度更新函数
  const updateProgress = useCallback((progressData: EventStreamItem) => {
    setProcessState((prev) => ({
      ...prev,
      current: progressData,
      showMessage: true,
    }));
    processRef.current = progressData;
  }, []);

  // 统一的错误处理函数
  const handleError = useCallback(
    async (error: any) => {
      console.error('Detection error:', error);

      setProcessState((prev) => ({
        ...prev,
        isPolling: false,
      }));

      const errorData: EventStreamItem = {
        type: 'error',
        message: 'Detection task failed',
        ts: Date.now().toLocaleString(),
        data: [],
        error: error,
      };

      updateProgress(errorData);
      setDisplayText(displayTextMap.detect_failed);

      await delay(5000);
      setDisplayText(displayTextMap.detect_contacts);
    },
    [updateProgress],
  );

  // EventSource logic
  const startEventSource = useCallback(
    async (taskId: string) => {
      // Cancel any previous request
      if (ctrlRef.current) {
        ctrlRef.current.abort();
      }

      const ctrl = new AbortController();
      ctrlRef.current = ctrl;

      const url = config.isLocal
        ? `${BASE_URL}/api/sales-agent/tasks/${taskId}/stream`
        : `${BASE_URL}/api/plm/common/sales-agent/tasks/${taskId}/stream`;

      try {
        await fetchEventSource(url, {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
            'x-api-key': X_API_KEY,
          },
          signal: ctrl.signal,

          onopen: async (response) => {
            if (response.ok) {
              return;
            } else if (response.status >= 400 && response.status < 500 && response.status !== 429) {
              console.error(
                `Client error on opening EventSource: ${response.status} ${response.statusText}`,
              );
              const errorData = {
                status: response.status,
                message: response.statusText || 'Client error',
              };
              await handleError(errorData);
              ctrl.abort();
              throw new Error(`Client error: ${response.status}`);
            } else {
              console.warn(
                `Server error on opening EventSource: ${response.status} ${response.statusText}. Retrying might occur.`,
              );
            }
          },

          onmessage: async (event: EventSourceMessage) => {
            try {
              const parsedData: EventStreamItem = JSON.parse(event.data);
              const { type, message, data: constantData, total } = parsedData;

              // 更新进度消息
              if (validMessageTypes.includes(type)) {
                updateProgress(parsedData);
              }

              // 更新显示文本
              if (type === 'start' || type === 'total') {
                if (message) {
                  setDisplayText(displayTextMap.detecting);
                }
              }

              // Handle completion
              if (['completed', 'success'].includes(type)) {
                const hasCompletionMessage =
                  parsedData && ['completed', 'success'].includes(parsedData.type);
                if (!hasCompletionMessage) {
                  const contactCount = total || (constantData && constantData.length) || 0;
                  const completionData: EventStreamItem = {
                    type,
                    message: type === 'completed' ? 'Detection completed' : 'Task completed',
                    ts: Date.now().toLocaleString(),
                    data: [contactCount],
                  };
                  updateProgress(completionData);
                }
              }

              // Handle errors
              if (['failed', 'error'].includes(type)) {
                console.error('Error message received:', parsedData);
                handleError(parsedData);
                ctrl.abort();
              }
            } catch (parseError) {
              console.error('Failed to parse event data as JSON:', event.data, parseError);
              ctrl.abort();
            }
          },

          onclose: async () => {
            await delay(100);

            const currentProcess = processRef.current;
            const isSuccess =
              currentProcess && ['completed', 'success'].includes(currentProcess.type);
            const isKnownError =
              currentProcess && ['failed', 'error'].includes(currentProcess.type);

            if (isSuccess) {
              await delay(5000);
              setDisplayText(displayTextMap.detect_contacts);
              reloadPage();
            } else if (!isKnownError) {
              console.warn('EventSource closed without explicit success/failure message.');
              await handleError({ message: 'Connection closed unexpectedly' });
            }
            ctrlRef.current = null;
          },

          onerror: (err) => {
            console.error('EventSource failed:', err);
            ctrlRef.current = null;

            if (err.name === 'AbortError') {
              console.log('EventSource fetch aborted manually.');
              return;
            }

            handleError(err).catch((e) =>
              console.error('Error in handleError from EventSource onerror:', e),
            );
          },
        });
      } catch (error: any) {
        if (error.name !== 'AbortError') {
          console.error('Error setting up or during fetchEventSource:', error);
          await handleError(error);
        } else {
          console.log('Fetch setup aborted.');
        }
        ctrlRef.current = null;
      }
    },
    [handleError, updateProgress],
  );

  // 检测任务处理逻辑
  const { loading: detectContactsLoading, run: handleDetectContacts } = useRequest(
    async (customPrompts?: CustomPromptsType) => {
      try {
        setDisplayText(displayTextMap.detecting);

        // 设置初始化进度
        updateProgress({
          type: 'start',
          message: 'Initializing detection task',
          ts: Date.now().toLocaleString(),
          data: [],
        });

        // 调用 startDetectTask 获取 task_id
        const { task_id: taskId } = await startDetectTask({
          account_id: accountId,
          custom_prompts: customPrompts,
        });

        if (taskId) {
          await startEventSource(taskId);
        } else {
          const errorData: EventStreamItem = {
            type: 'error',
            message: 'Failed to initialize detection task',
            ts: Date.now().toLocaleString(),
            data: [],
          };
          updateProgress(errorData);
          setDisplayText(displayTextMap.detect_failed);
          new Error('Failed to get task ID');
        }
      } catch (error) {
        console.error('Error when start detect task:', error);

        const errorData: EventStreamItem = {
          type: 'error',
          message: 'Error occurred during detection task',
          ts: Date.now().toLocaleString(),
          error: String(error),
          data: [],
        };
        updateProgress(errorData);
        await handleError(error);
        throw error;
      }
    },
    {
      manual: true,
      onError: async (error) => {
        console.error('onError when start detect task:', error);

        const errorData: EventStreamItem = {
          type: 'error',
          message: 'Error occurred during detection task',
          ts: Date.now().toLocaleString(),
          error: String(error),
          data: [],
        };
        updateProgress(errorData);
        await handleError(error);
      },
    },
  );

  // 统一的任务状态查询和处理逻辑
  const { loading: queryTaskStatusLoading, run: handleTaskStatusQuery } = useRequest(
    async (isPollingQuery: boolean = false): Promise<void> => {
      try {
        if (!isPollingQuery) {
          setDisplayText(displayTextMap.query_task_status);
        }

        // 设置查询状态进度
        updateProgress({
          type: 'start',
          message: 'Querying detection task status',
          ts: Date.now().toLocaleString(),
          data: [],
        });

        // 获取任务状态
        const resultData: ResultDataItem = await detectTaskStatus(accountId);
        const { status, events } = resultData;

        // 设置任务状态结果
        updateProgress({
          ...resultData.result,
          type: status,
        } as EventStreamItem);

        // 更新显示文本
        setDisplayText(displayTextMap[status as keyof typeof displayTextMap]);

        // 根据状态决定是否继续轮询和后续操作
        if (status === 'pending' || status === 'running') {
          // 取出 events 中最后一条数据，然后添加到 processState 中
          const lastEvent = last(events);
          processRef.current = lastEvent as unknown as EventStreamItem;
          setProcessState((prev) => ({
            ...prev,
            isPolling: true,
            current: lastEvent as unknown as EventStreamItem,
          }));
        } else if (['completed', 'success'].includes(status)) {
          setProcessState((prev) => ({ ...prev, isPolling: false }));

          if (isPollingQuery) {
            await delay(5000);
            setDisplayText(displayTextMap.detect_contacts);
            reloadPage();
          } else {
            await delay(5000);
            setDisplayText(displayTextMap.detect_contacts);
          }
        } else if (['failed', 'canceled', 'error'].includes(status)) {
          setProcessState((prev) => ({ ...prev, isPolling: false }));

          if (isPollingQuery) {
            await delay(5000);
          }
          setDisplayText(displayTextMap.detect_contacts);
        } else {
          setProcessState((prev) => ({ ...prev, isPolling: false }));
          setDisplayText(displayTextMap.detect_contacts);
        }
      } catch (error) {
        setProcessState((prev) => ({ ...prev, isPolling: false }));
        await handleError(error);
      }
    },
    {
      manual: true,
      refreshDeps: [],
    },
  );

  // 轮询任务状态 - 当 isPolling 为 true 且 status 为 pending 或 running 时每 5 秒刷新
  useInterval(
    () => {
      handleTaskStatusQuery(true);
    },
    processState.isPolling ? 5000 : undefined, // 当 isPolling 为 false 时传入 undefined 取消定时器
    {
      immediate: false, // 不立即执行
    },
  );

  // 组件加载时执行第一次查询
  useEffect(() => {
    if (accountId) {
      handleTaskStatusQuery(false);
    }
  }, [accountId, handleTaskStatusQuery]);

  // EventSource 清理逻辑
  useUnmount(() => {
    if (ctrlRef.current) {
      ctrlRef.current.abort();
      ctrlRef.current = null;
    }
  });

  // 监听进度变化，控制消息显示
  useEffect(() => {
    const { type } = processState.current ?? {};
    if (type && ['success', 'completed'].includes(type)) {
      setProcessState((prev) => ({ ...prev, showMessage: true }));
      const timer = setTimeout(() => {
        setProcessState((prev) => ({ ...prev, showMessage: false }));
      }, 5000);
      return () => clearTimeout(timer);
    } else if (type === 'error') {
      setProcessState((prev) => ({ ...prev, showMessage: false }));
    } else if (type) {
      setProcessState((prev) => ({ ...prev, showMessage: true }));
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [processState?.current?.type]);

  const handleStandardDetect = async (data?: CustomPromptsType) => {
    setDisplayText('Detect Button Clicked');

    // 停止轮询，开始点击事件流程
    setProcessState((prev) => ({ ...prev, isPolling: false }));

    // 设置点击事件的初始进度
    updateProgress({
      type: 'start',
      message: 'Initializing detection process',
      ts: Date.now().toLocaleString(),
      data: [],
    });

    await handleDetectContacts(data);
  };

  const isLoading = queryTaskStatusLoading || detectContactsLoading || processState.isPolling;

  console.log('anchor accountId -> ', accountId);

  if (!accountId) {
    console.warn('DetectContacts: accountId is missing, returning null');
    return null;
  }

  return (
    <div style={{ display: 'inline-block' }}>
      <Space size={4}>
        <Dropdown
          disabled={isLoading}
          getPopupContainer={(node) => node.parentElement as HTMLElement}
          menu={{
            items: [
              {
                key: 'standard',
                onClick: () => handleStandardDetect(),
                label: 'Use Standard Detect',
                disabled: isLoading,
              },
              {
                key: 'custom',
                disabled: isLoading,
                onClick: () => setCustomPromptModalOpen(true),
                label: 'Use Custom AI Prompt',
              },
            ],
          }}
          overlayStyle={{
            width: 180,
          }}
        >
          <Button
            type='default'
            size='small'
            icon={<img src={MagicSVG} alt='magic' width={20} style={{ color: '#fff' }} />}
            disabled={isLoading}
            loading={isLoading}
            style={{
              marginLeft: '8px',
              display: 'inline-flex',
              alignItems: 'center',
              gap: '8px',
              verticalAlign: 'middle',
              height: '24px',
              lineHeight: '24px',
            }}
            onClick={(event) => event.preventDefault()}
          >
            {displayText}
            <DownOutlined />
          </Button>
        </Dropdown>

        {processState.current && processState.showMessage && (
          <Typography.Text
            style={{
              marginLeft: '8px',
              color: '#666',
              fontSize: '14px',
              lineHeight: '32px',
              verticalAlign: 'middle',
              display: 'inline-block',
              width: 'max-content',
            }}
          >
            {formatMessage(
              processState.current.type,
              processState.current.message || '',
              processState.current.data,
            )}
          </Typography.Text>
        )}
      </Space>

      {customPromptModalOpen && (
        <CustomPromptModalModal
          open={customPromptModalOpen}
          onConfirm={(data: CustomPromptsType) => {
            handleStandardDetect(data);
          }}
          onCancel={() => setCustomPromptModalOpen(false)}
        />
      )}
    </div>
  );
};

export default DetectContacts;
