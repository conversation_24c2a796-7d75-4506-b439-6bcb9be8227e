import { CustomPromptsType } from '@/components/custom-prompt-modal';
import config from '@/config';
import request from '@/utils/request';

export interface CompanyInfoType {
  account_id: string;
  custom_prompts?: CustomPromptsType;
}

export const detectTaskStatus = async (accountId: string) => {
  const url = config.isLocal
    ? `/api/sales-agent/${accountId}/result`
    : `/api/plm/common/sales-agent/${accountId}/result`;
  return await request(url, {
    method: 'GET',
  });
};

export const startDetectTask = async (companyInfo: CompanyInfoType) => {
  const url = config.isLocal ? '/api/sales-agent/start' : '/api/plm/common/sales-agent/start';
  return await request(url, {
    method: 'POST',
    data: companyInfo,
  });
};
