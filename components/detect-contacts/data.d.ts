export interface EventStreamItem {
  type: ResultEventItemType;
  name?: string;
  message: string;
  ts: string;
  data?: unknown[];
  status?: TaskStatusType;
  total?: number;
  current?: number;
  error?: string;
}

// result data structure
export type TaskStatusType =
  | 'pending'
  | 'running'
  | 'completed'
  | 'failed'
  | 'canceled'
  | 'success';

export type ResultEventItemType =
  | 'start'
  | 'total'
  | 'latent'
  | 'filter_latent'
  | 'get_contacts_info'
  | 'update_contacts_info'
  | 'get_completed_contacts_data'
  | 'ready_data_transform'
  | 'data_transform'
  | 'completed'
  | 'success'
  | 'error';

export interface ResultEventItem {
  type: ResultEventItemType;
  name: string;
  ts: string;
  details: {
    source: string[];
    data: unknown[];
    total: number;
  };
}

export interface ResultDataItem {
  _id: string;
  status: TaskStatusType;
  type: 'sales_agent';
  created_at: string;
  updated_at: string;
  tags: {
    account_id: string;
    task_type: 'sales_agent';
    custom_prompts?: {
      prompt: string;
      cover_system_prompt?: boolean;
    };
  };
  started_at: string;
  error: string | null;
  events: ResultEventItem[];
  completed_at: string;
  result?: EventStreamItem;
}
