// Display text mapping
export const displayTextMap = {
  detect_contacts: 'Detect Contacts',
  detecting: 'Detecting...',
  query_task_status: 'Query Task Status...',
  completed: 'Completed',
  detect_failed: 'Detect Failed',
  pending: 'Pending',
  running: 'Processing',
  processing: 'Processing',
  success: 'Completed',
  failed: 'Failed',
  error: 'Failed',
  '500': 'Failed',
  unknown: 'Failed',
  connected: 'Detecting...',
  info: 'Detecting...',
  start: 'Detecting...',
  total: 'Detecting...',
  latent: 'Detecting...',
  filter_latent: 'Detecting...',
  get_contacts_info: 'Detecting...',
  update_contacts_info: 'Detecting...',
  get_completed_contacts_data: 'Detecting...',
  ready_data_transform: 'Detecting...',
  data_transform: 'Detecting...',
  queued: 'In queue',
  canceled: 'Canceled',
  not_found: 'Task not found',
};

// Valid message types list
export const validMessageTypes = [
  'connected',
  'info',
  'start',
  'total',
  'latent',
  'filter_latent',
  'get_contacts_info',
  'update_contacts_info',
  'get_completed_contacts_data',
  'ready_data_transform',
  'data_transform',
  'success',
  'completed',
  'error',
  'failed',
  'not_found',
];
