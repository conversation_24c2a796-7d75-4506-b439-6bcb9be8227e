import { StyleProvider } from '@ant-design/cssinjs';
import ReactDOM from 'react-dom/client';

import DetectContacts from './index.tsx';

import { ContentScriptContext, createShadowRootUi } from '#imports';

// Maintain a global state to track if the UI is mounted
let globalIsUiMounted = false;
let currentUi: any = null;
let currentRoot: ReactDOM.Root | null = null;

const createDetectButtonUi = (ctx: ContentScriptContext) => {
  const anchorSelector =
    document.querySelector(
      'div[lt-prop-landmark-label="Complementary: Contacts"] .dvcompHeader span[aria-label="Contacts"]',
    ) ||
    document.querySelector(
      'div[lt-prop-landmark-label="Complementary: Contacts"] .relListHeader span[data-zcqa="Related_List_Name_Contacts"]',
    );

  if (!anchorSelector) {
    console.error('❌ No anchor element found!');
    return null;
  }

  console.log('🚀 Creating shadow root UI...');

  try {
    const ui = createShadowRootUi(ctx, {
      name: 'inhand-detect-button-ui',
      position: 'inline',
      anchor: anchorSelector,
      append: 'after',
      isolateEvents: false,
      onMount: (container: HTMLElement, shadow: ShadowRoot, shadowHost: HTMLElement) => {
        console.log('✅ onMount called!');

        if (shadowHost) {
          shadowHost.style.display = 'inline-block';
          shadowHost.style.position = 'relative';
          shadowHost.style.marginLeft = '8px';
          shadowHost.style.verticalAlign = 'middle';
          shadowHost.classList.add('inhand-detect-button-host');
        }

        if (container) {
          container.style.display = 'inline-block';
          container.style.position = 'relative';
          container.style.verticalAlign = 'middle';

          const reactContainer = document.createElement('div');
          reactContainer.id = 'detect-contacts-container';
          reactContainer.style.display = 'inline-block';
          reactContainer.style.verticalAlign = 'middle';

          // 确保 shadow DOM 有 head 元素
          let shadowHead = shadow.querySelector('head');
          if (!shadowHead) {
            shadowHead = document.createElement('head');
            shadow.appendChild(shadowHead);
          }

          currentRoot = ReactDOM.createRoot(reactContainer);

          currentRoot?.render(
            <StyleProvider container={shadowHead}>
              <DetectContacts />
            </StyleProvider>,
          );
          container.appendChild(reactContainer);
        }

        return container;
      },
      onRemove: (mounted: HTMLElement | undefined) => {
        console.log('🗑️ onRemove called');
        if (mounted) {
          if (currentRoot) {
            currentRoot.unmount();
            currentRoot = null;
          }
          globalIsUiMounted = false;
          currentUi = null;
        }
      },
    });

    console.log('🎯 UI created:', ui);
    return ui;
  } catch (error) {
    console.error('💥 Error creating UI:', error);
    return null;
  }
};

// Reset the mounted state - can be called when navigating between pages
export const resetUIState = () => {
  globalIsUiMounted = false;
  if (currentRoot) {
    currentRoot.unmount();
    currentRoot = null;
  }
  currentUi = null;
};

export const renderDetectContacts = (ctx: ContentScriptContext) => {
  // clean up the previous ui
  const existingUI = document.querySelector('.inhand-detect-button-host');
  if (existingUI) {
    existingUI.remove();
    globalIsUiMounted = false;
    currentUi = null;
    if (currentRoot) {
      currentRoot.unmount();
      currentRoot = null;
    }
  }

  resetUIState();

  let isProcessing = false;

  const observerContactHeader = async () => {
    if (isProcessing) {
      console.log('⏳ Already processing, skipping...');
      return;
    }

    isProcessing = true;
    console.log('🔄 Starting observer...');

    try {
      const waitForElement = (selector: string, timeout = 10000): Promise<Element | null> => {
        return new Promise((resolve) => {
          console.log(`🔍 Waiting for element: ${selector}`);

          const element = document.querySelector(selector);
          if (element) {
            console.log(`✅ Element found immediately: ${selector}`);
            resolve(element);
            return;
          }

          const observer = new MutationObserver(() => {
            const element = document.querySelector(selector);
            if (element) {
              console.log(`✅ Element found via observer: ${selector}`);
              observer.disconnect();
              resolve(element);
            }
          });

          observer.observe(document.body, {
            childList: true,
            subtree: true,
          });

          setTimeout(() => {
            console.log(`⏰ Timeout waiting for: ${selector}`);
            observer.disconnect();
            resolve(null);
          }, timeout);
        });
      };

      const selectors = [
        'div[lt-prop-landmark-label="Complementary: Contacts"] .dvcompHeader span[aria-label="Contacts"]',
        'div[lt-prop-landmark-label="Complementary: Contacts"] .relListHeader span[data-zcqa="Related_List_Name_Contacts"]',
      ];

      for (const selector of selectors) {
        const anchor = await waitForElement(selector, 20000);
        if (anchor && !globalIsUiMounted) {
          console.log('🎯 Target anchor found, creating UI...', anchor);
          globalIsUiMounted = true;

          currentUi = await createDetectButtonUi(ctx);
          console.log('🔧 UI instance created:', currentUi);

          if (currentUi) {
            console.log('🚀 Mounting UI...');
            const mountResult = currentUi.mount();
            console.log('📌 Mount result:', mountResult);
            console.log('✅ UI should be mounted now');
          } else {
            console.error('❌ Failed to create UI instance');
          }
          break;
        }
      }
    } catch (error) {
      console.error('💥 Error in observer:', error);
    } finally {
      isProcessing = false;
      console.log('🏁 Observer finished');
    }
  };
  observerContactHeader();
};
