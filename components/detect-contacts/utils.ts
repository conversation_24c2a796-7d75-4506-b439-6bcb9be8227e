// Type to message mapping for displaying user-friendly messages
export const typeToMessageMap: Record<string, string> = {
  connected: 'Connected to server',
  info: 'Preparing detection task',
  start: 'Starting data processing',
  total: 'Fetching employee data',
  latent: 'Retrieving potential contacts',
  filter_latent: 'Potential contacts identified',
  get_contacts_info: 'Completing contact information',
  update_contacts_info: 'Updating contact details',
  get_completed_contacts_data: 'Finalizing contact data',
  ready_data_transform: 'Preparing data transformation',
  data_transform: 'Adding contacts to system',
  success: 'Task completed successfully',
  completed: 'Detection completed successfully',
  error: 'Error occurred',
  failed: 'Operation failed',
  not_found: 'Session not found',
  // Note: "ping" type is intentionally excluded as we don't want to display it
};

/**
 * Format message with variables based on type and data
 */
export const formatMessage = (type: string, message: string, data: any): string => {
  // Get the template message from the map
  let template = typeToMessageMap[type] || message;

  // If no template found, return the original message
  if (!template) return message;

  // Replace variables in the template
  if (type === 'total' && data?.name) {
    template = template.replace('employee data', `${data.name}'s employee data`);
  } else if (type === 'latent' && data?.total) {
    template = `Retrieving potential contacts from ${data.total} contacts`;
  } else if (type === 'filter_latent' && data?.total) {
    template = `${data.total} potential contacts identified`;
  } else if (type === 'data_transform' && data) {
    const current = data.current || 0;
    const total = data.total || data.length || 0;
    template = `Adding contacts to system (${current}/${total})`;
  } else if ((type === 'success' || type === 'completed') && data) {
    const total = data.total || data.length || 0;
    if (type === 'success') {
      template = `Task completed successfully. Added ${total} contacts`;
    } else if (type === 'completed') {
      template = `Detection completed successfully. Added ${total} contacts`;
    }
  }

  return template;
};

/**
 * Get timeline item color based on a message type
 */
export const getTimelineItemColor = (type: string): string => {
  switch (type) {
    case 'connected':
      return 'blue';
    case 'info':
      return 'green';
    case 'start':
      return 'purple';
    case 'total':
      return 'orange';
    case 'latent':
    case 'filter_latent':
    case 'get_contacts_info':
    case 'update_contacts_info':
    case 'get_completed_contacts_data':
    case 'ready_data_transform':
    case 'data_transform':
      return 'blue';
    case 'completed':
    case 'success':
      return '#13c2c2'; // cyan
    case 'error':
    case 'failed':
      return 'red';
    case 'not_found':
      return 'gray';
    default:
      return 'blue';
  }
};
