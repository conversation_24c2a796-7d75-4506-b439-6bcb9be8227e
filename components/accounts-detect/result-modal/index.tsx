import { useCountDown, useMount } from 'ahooks';
import {
  <PERSON>ert,
  Button,
  Descriptions,
  DescriptionsProps,
  Modal,
  ModalProps,
  Tag,
  Typography,
} from 'antd';
import React, { useMemo } from 'react';
import { useCopyToClipboard } from 'react-use';

import { BatchProcessResult } from '../batch-button/batch-server.ts';

interface ResultModalProps extends ModalProps {
  data: BatchProcessResult[];
  failedAccounts?: BatchProcessResult[];
  containerElement: HTMLElement;
}

const resultValueMap = {
  completed: {
    text: 'Success',
    color: 'green',
  },
  success: {
    text: 'Success',
    color: 'green',
  },
  failed: {
    text: 'Failed',
    color: 'red',
  },
  processing: {
    text: 'Processing',
    color: 'blue',
  },
  not_started: {
    text: 'Not Started',
    color: 'gray',
  },
  error: {
    text: 'Error',
    color: 'red',
  },
  unknown: {
    text: 'Unknown',
    color: 'gray',
  },
};

const ResultModal: React.FC<ResultModalProps> = ({
  data,
  failedAccounts,
  containerElement,
  ...rest
}) => {
  const [, copyToClipboard] = useCopyToClipboard();
  const [targetDate, setTargetDate] = useState<number>();

  useMount(() => {
    setTargetDate(Date.now() + 60 * 1000);
  });

  const items: DescriptionsProps['items'] = useMemo(() => {
    return data.map((item) => {
      const status = (item.status ?? 'unknown').toLocaleLowerCase() as keyof typeof resultValueMap;
      return {
        key: item.accountId,
        label: item.name,
        children: <Tag color={resultValueMap[status].color}>{resultValueMap[status].text}</Tag>,
      };
    });
  }, [data]);

  const [countdown] = useCountDown({
    targetDate,
    onEnd: () => {
      window.location.reload();
    },
  });

  const AlertMessage = useMemo(
    () => (
      <Alert
        type='warning'
        showIcon
        message={
          <Typography>
            <Typography.Text>
              The page will be reloaded in {Math.round(countdown / 1000)} seconds.{' '}
            </Typography.Text>
            <Typography.Text>
              If you don&#39;t want to reload the page, please directly close the modal. The page
              will not be reloaded, and at the same time, the number of contacts will not be
              updated.
            </Typography.Text>
          </Typography>
        }
        style={{ marginBottom: 8 }}
      />
    ),
    [countdown],
  );

  return (
    <Modal
      title='Batch Detect Result'
      {...rest}
      destroyOnHidden
      closable={false}
      maskClosable={false}
      cancelText='Close'
      getContainer={containerElement}
      okText='Reload'
      onOk={() => window.location.reload()}
    >
      {AlertMessage}
      <Descriptions
        size='small'
        items={items}
        column={1}
        styles={{
          content: {
            maxHeight: '600px',
            overflowY: 'auto',
          },
        }}
        extra={
          failedAccounts &&
          failedAccounts?.length > 0 && (
            <Button
              onClick={() => {
                const copyInfo = failedAccounts.map((item) => item.name)?.join(', ');
                copyToClipboard(copyInfo);
              }}
            >
              Copy Failed Accounts
            </Button>
          )
        }
      />
    </Modal>
  );
};

export default ResultModal;
