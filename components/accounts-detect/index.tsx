import { StyleProvider } from '@ant-design/cssinjs';
import { delay, difference, omit } from 'es-toolkit';
import ReactDOM from 'react-dom/client';

import { AccountInfoItem } from '@/components/accounts-detect/data';
import { detectTaskStatus } from '@/components/detect-contacts/service';
import { accountListPattern } from '@/entrypoints/content';

import { TaskStatusType } from '../detect-contacts/data';

import BatchButton from './batch-button';
import { accountStatusUIMap, mountDisplayStatusUI } from './display-status';

import { ContentScriptContext, createShadowRootUi } from '#imports'; // Import WXT UI helpers

// 等待DOM元素渲染完成的通用函数
const waitForElement = (
  selector: string,
  validateFn?: (element: Element) => boolean,
  timeout = 10000,
): Promise<Element | null> => {
  return new Promise((resolve) => {
    // 如果元素已存在并且通过验证，立即返回
    const element = document.querySelector(selector);
    if (element && (!validateFn || validateFn(element))) {
      return resolve(element);
    }

    // 设置超时
    const timeoutId = setTimeout(() => {
      observer.disconnect();
      resolve(null);
    }, timeout);

    // 使用MutationObserver监听DOM变化
    const observer = new MutationObserver((_, obs) => {
      const element = document.querySelector(selector);
      if (element && (!validateFn || validateFn(element))) {
        clearTimeout(timeoutId);
        obs.disconnect();
        resolve(element);
      }
    });

    observer.observe(document.body, {
      childList: true,
      subtree: true,
    });
  });
};

// 验证单元格是否包含所需的属性
const validateAccountNameCellIsReady = (accountId: string, cells: NodeListOf<Element>): boolean => {
  // 检查是否有账户名称单元格
  // 找到 cells 的子元素 包含 <a id=`listView_{accountId}` 的标签
  const result: Element[] = [];
  cells.forEach((v) => {
    if (v.querySelector(`#listView_${accountId}`)) {
      result.push(v);
    }
  });
  return result?.length > 0;
};

// 等待表格行中的单元格完全渲染的函数
const waitForTableCellsToBeReady = (
  accountId: string,
  row: Element,
  timeout = 10000,
): Promise<NodeListOf<Element> | null> => {
  return new Promise((resolve) => {
    // 获取单元格
    const cells = row.querySelectorAll('lyte-exptable-td');

    // 验证单元格是否已准备好（包含所需的属性）
    if (cells && cells.length > 0 && validateAccountNameCellIsReady(accountId, cells)) {
      return resolve(cells);
    }

    // 设置超时
    const timeoutId = setTimeout(() => {
      observer.disconnect();
      resolve(null);
    }, timeout);

    // 使用MutationObserver监听单元格变化
    const observer = new MutationObserver(() => {
      // 检查是否有相关属性变化
      const cells = row.querySelectorAll('lyte-exptable-td');
      if (cells && cells.length > 0 && validateAccountNameCellIsReady(accountId, cells)) {
        clearTimeout(timeoutId);
        observer.disconnect();
        resolve(cells);
      }
    });

    // 监听行及其子元素的变化
    observer.observe(row, {
      childList: true,
      subtree: true,
      attributes: true,
      attributeFilter: ['aria-label', 'lt-prop-value'], // 监听特定属性的变化
    });
  });
};

const handleUpdateStatus = (ctx: ContentScriptContext, accountId: string, data: any) => {
  mountDisplayStatusUI({
    ctx,
    accountId,
    data,
  });
};

const batchDetectUiSingleton = {
  ui: null as any,
  updateProps: null as
    | ((props: { selectedAccountInfo: Record<string, AccountInfoItem> }) => void)
    | null,
  isMounted: false,
};

export const createBatchDetectUi = async (
  ctx: ContentScriptContext,
  {
    selectedAccountInfo,
  }: {
    selectedAccountInfo: Record<string, AccountInfoItem>;
  },
) => {
  // 如果UI已经挂载，只需更新props
  if (batchDetectUiSingleton.isMounted && batchDetectUiSingleton.updateProps) {
    batchDetectUiSingleton.updateProps({ selectedAccountInfo });

    // 控制UI可见性
    const hostElement = document.querySelector('.inhand-injected-element') as HTMLElement | null;
    if (hostElement) {
      hostElement.style.display =
        selectedAccountInfo && Object.keys(selectedAccountInfo).length > 0
          ? 'inline-block'
          : 'none';
    }

    return batchDetectUiSingleton.ui;
  }

  // 等待body元素加载完成
  const bodyAnchorElement = document.body;

  if (!bodyAnchorElement) {
    console.error('Target anchor element (body) not found');
    return null;
  }

  return createShadowRootUi(ctx, {
    name: 'inhand-batch-detect-ui',
    position: 'inline',
    anchor: bodyAnchorElement,
    onMount: (container: HTMLElement, shadow: ShadowRoot) => {
      try {
        const hostElement = (container.getRootNode() as ShadowRoot)?.host as
          | HTMLElement
          | undefined;

        // 设置host元素样式
        if (hostElement) {
          hostElement.style.display =
            selectedAccountInfo && Object.keys(selectedAccountInfo).length > 0
              ? 'inline-block'
              : 'none';
          hostElement.classList.add('inhand-injected-element');
          hostElement.setAttribute('data-inhand-injected', 'true');
          hostElement.style.position = 'absolute';
          hostElement.style.zIndex = '1000';
          hostElement.style.top = '10px';
          hostElement.style.right = '20px';
        }

        const cssContainer = shadow.querySelector('head')!;

        if (container) {
          // 添加事件隔离
          const eventShield = document.createElement('div');
          eventShield.className = 'event-shield';
          container.appendChild(eventShield);

          // 创建一个容器元素，所有渲染都在这个容器内
          const reactContainer = document.createElement('div');
          reactContainer.id = 'batch-button-container';
          container.appendChild(reactContainer);

          // 创建React根
          const root = ReactDOM.createRoot(reactContainer);

          // 创建一个函数来更新props
          batchDetectUiSingleton.updateProps = (newProps) => {
            // 控制UI可见性
            const hostElement = document.querySelector(
              'inhand-batch-detect-ui',
            ) as HTMLElement | null;

            if (hostElement) {
              const currentUrl = window.location.origin + window.location.pathname;
              const isOnListPage = accountListPattern.includes(currentUrl);

              hostElement.style.display =
                newProps.selectedAccountInfo &&
                Object.keys(newProps.selectedAccountInfo).length > 0 &&
                isOnListPage
                  ? 'inline-block'
                  : 'none';
            }
            // 更新UI
            root.render(
              <StyleProvider container={cssContainer}>
                <BatchButton
                  selectedAccountInfo={newProps.selectedAccountInfo || {}}
                  onUpdateStatus={(accountId, data) => {
                    // 批量发起 status 请求
                    handleUpdateStatus(ctx, accountId, data);
                  }}
                />
              </StyleProvider>,
            );
          };

          // 首次渲染
          batchDetectUiSingleton.updateProps({ selectedAccountInfo });
          batchDetectUiSingleton.isMounted = true;
        }
      } catch (error) {
        console.error('Error in onMount:', error);
      }

      return container;
    },
    onRemove: (container: HTMLElement | undefined) => {
      try {
        // 清理React根并重置singleton状态
        if (container) {
          const reactContainer = container.querySelector('#batch-button-container');
          if (reactContainer) {
            ReactDOM.createRoot(reactContainer).unmount();
          }
        }
        batchDetectUiSingleton.isMounted = false;
        batchDetectUiSingleton.updateProps = null;
      } catch (error) {
        console.error('Error in onRemove:', error);
      }
    },
  });
};

// 当选中账户发生变化时触发的函数
const handleCheckedAccountsChange = async (
  selectedAccountInfo: Record<string, AccountInfoItem>,
) => {
  batchDetectUiSingleton.updateProps?.({ selectedAccountInfo });
};

// 初始化全局变量
(window as any).inhandBatchDetectSelectedAccountInfos =
  (window as any).inhandBatchDetectSelectedAccountInfos || {};
(window as any).inhandBatchDetectMountUI = (window as any).inhandBatchDetectMountUI || null;

const renderBatchDetectUi = async (ctx: ContentScriptContext) => {
  const selectedAccountInfo: Record<string, AccountInfoItem> = (window as any)
    .inhandBatchDetectSelectedAccountInfos; // 跟踪所有账户信息

  // 监控 对 .lyteExpTableOrigTableInnerWrap 添加一个全局的 click 事件的监控
  await waitForElement('.lyteExpTableOrigTableInnerWrap');
  const lyteExpTableWrapper = document.querySelector('.lyteExpTableOrigTableInnerWrap');
  if (lyteExpTableWrapper) {
    await waitForElement('.lyteExpTableRowGroup');
    const lyteExpTableRowGroup = document.querySelector('.lyteExpTableRowGroup');
    // 监控 lyteExpTableRowGroup 下， lyte-exptable-tr 的 class 变化
    if (lyteExpTableRowGroup) {
      const obs = new MutationObserver(() => {
        const lyteExpTableTrs = lyteExpTableRowGroup.querySelectorAll('lyte-exptable-tr');
        const listViewRowSelectedTrs = Array.from(lyteExpTableTrs).filter((tr) =>
          tr.classList.contains('listViewRowSelected'),
        );
        // 找到 lyteExpTableTrs 的所有id
        const currentPageIds = Array.from(lyteExpTableTrs).map(
          (tr) => tr.getAttribute('id') as string,
        );

        if (listViewRowSelectedTrs.length > 0) {
          // 获取 listViewRowSelectedTrs 的 id 属性
          const tempCheckedIds: string[] = [];
          const tempAllAccountInfo: Record<string, AccountInfoItem> = {};
          listViewRowSelectedTrs.forEach((tr) => {
            const id = tr.getAttribute('id') as string;
            // extract account info
            const { name: accountName } = getAccountNameCellInfo(id) ?? {};

            tempCheckedIds.push(id);
            tempAllAccountInfo[id] = {
              id,
              name: accountName as string,
            };
          });

          const uncheckedIds = difference(currentPageIds, tempCheckedIds);

          // merge tempAllAccountInfo to window.inhandBatchDetectSelectedAccountInfos,
          (window as any).inhandBatchDetectSelectedAccountInfos = omit(
            {
              ...(window as any).inhandBatchDetectSelectedAccountInfos,
              ...tempAllAccountInfo,
            },
            uncheckedIds,
          );
        } else {
          // 去除 window.inhandBatchDetectSelectedAccountInfos 中 currentPageIds 对应的值
          currentPageIds.forEach((id) => {
            delete (window as any).inhandBatchDetectSelectedAccountInfos[id];
          });
        }

        handleCheckedAccountsChange((window as any).inhandBatchDetectSelectedAccountInfos);
      });
      obs.observe(lyteExpTableRowGroup, {
        childList: true,
        subtree: true,
        attributes: true,
        attributeFilter: ['class'],
      });
    }
  }

  let isMountedUI = false;

  const inhandBatchDetectUi = document.getElementsByTagName('inhand-batch-detect-ui')?.[0];

  if (!inhandBatchDetectUi) {
    const mountUI = async () => {
      if (isMountedUI) return;
      isMountedUI = true;
      return await createBatchDetectUi(ctx, {
        selectedAccountInfo,
      });
    };
    (window as any).inhandBatchDetectMountUI = await mountUI();
    // 存储UI引用并立即挂载
    batchDetectUiSingleton.ui = (window as any).inhandBatchDetectMountUI;
    (window as any).inhandBatchDetectMountUI.mount();
  }

  // 触发账户选中变化的处理函数
  await handleCheckedAccountsChange(selectedAccountInfo);
};

export const getAccountNameCellInfo = (accountId: string) => {
  const basicElm = document.getElementById(`listView_${accountId}`);
  if (basicElm) {
    return {
      name: basicElm?.getAttribute('data-zcqa'),
      cell: basicElm.parentElement?.parentElement,
    };
  }
  return {};
};

const terminalStatusTypes = [
  'failed',
  'error',
  'completed',
  'success',
  'unknown',
  '500',
  'not_found',
];

const fetchAndMountUI = async (ctx: ContentScriptContext, accountId: string) => {
  let currentStatus = await detectTaskStatus(accountId);

  // Initial mount of the UI
  await mountDisplayStatusUI({ ctx, accountId, data: currentStatus });

  // Start polling if the status is not terminal
  while (
    currentStatus &&
    currentStatus.status &&
    !terminalStatusTypes.includes(currentStatus.status?.toString())
  ) {
    await delay(5000); // Wait for 5 seconds

    try {
      const newStatus = await detectTaskStatus(accountId);
      if (newStatus && currentStatus.status) {
        currentStatus = newStatus;
        // Update the UI with the new status
        await mountDisplayStatusUI({ ctx, accountId, data: currentStatus });

        // Break the loop if the new status is terminal
        if (terminalStatusTypes.includes(currentStatus.status?.toString())) {
          break;
        }
      } else {
        // Handle a case where status fetches returns null or undefined, maybe break or log
        console.warn(`Failed to fetch status for account ${accountId} during polling.`);
        break; // Exit loop if status fetch fails
      }
    } catch (error) {
      console.error(`Error polling status for account ${accountId}:`, error);
      // Decide how to handle errors, e.g., break the loop
      await mountDisplayStatusUI({
        ctx,
        accountId,
        data: {
          type: 'sales_agent',
          status: 'failed' as TaskStatusType,
          error: error as string,
          completed_at: Date.now().toString(),
          _id: accountId,
          created_at: Date.now().toString(),
          started_at: Date.now().toString(),
          updated_at: Date.now().toString(),
          tags: {
            account_id: accountId,
            task_type: 'sales_agent',
          },
          events: [
            {
              type: 'error',
              name: 'Polling failed',
              ts: Date.now().toString(),
              details: {
                source: [],
                data: [],
                total: 0,
              },
            },
          ],
        },
      });
      break;
    }
  }
};

const renderStatusUIInList = (ctx: ContentScriptContext) => {
  let mountedAccountIds: Record<string, boolean> = {}; // 跟踪已处理的账户ID
  let isProcessing = false;

  // 处理表格行
  const processTableRows = async (rows: NodeListOf<Element>) => {
    for (const row of rows) {
      const accountId = row.getAttribute('id') as string;
      if (accountId) {
        // 等待该行的单元格完全渲染
        const cells = await waitForTableCellsToBeReady(accountId, row);

        if (!cells) {
          console.warn(`Timeout waiting for cells in row ${accountId} to be ready`);
          continue;
        }
        // 开始 监控 account name 行
        const nameCellElm = document.getElementById(`listView_${accountId}`);
        if (!nameCellElm) {
          console.warn(`Account name cell not found for account ID: ${accountId}`);
          continue;
        }

        // 只有当该账户ID尚未被处理时才设置观察器
        if (!mountedAccountIds[accountId]) {
          const nameCellObs = new MutationObserver(() => {
            // extract account info
            const { name: accountName } = getAccountNameCellInfo(accountId);
            if (accountName) {
              // 标记为已处理
              mountedAccountIds[accountId] = true;

              // 一旦处理完成，立即断开观察器连接，确保只执行一次
              nameCellObs.disconnect();

              fetchAndMountUI(ctx, accountId);
            }
          });

          // 检查当前是否已有账户名，如果有则直接处理
          const { name: accountName } = getAccountNameCellInfo(accountId);
          if (accountName) {
            // 标记为已处理
            mountedAccountIds[accountId] = true;

            // 异步获取存储数据并挂载UI
            (() => {
              fetchAndMountUI(ctx, accountId);
            })();
          } else {
            // 如果当前没有账户名，则开始观察变化
            nameCellObs.observe(nameCellElm, {
              attributes: true,
              attributeFilter: ['aria-label', 'lt-prop-value'], // 监听特定属性的变化
            });
          }
        }
      }
    }
  };

  // Function to process table rows
  const processTable = async (targetTableComponent: Element) => {
    if (!targetTableComponent) return;

    const tableRowsElm = targetTableComponent.querySelector('.lyteExpTableRowGroup');

    if (tableRowsElm) {
      // 查找表格行
      const rows = tableRowsElm.querySelectorAll('lyte-exptable-tr');
      if (rows && rows.length > 1) {
        // 监控 tableRowsElm
        const observer = new MutationObserver(async () => {
          const newRows = tableRowsElm.querySelectorAll('lyte-exptable-tr');
          await processTableRows(newRows);
        });

        observer.observe(tableRowsElm, {
          childList: true,
        });
        await processTableRows(rows);
      }
    }
  };

  const injectDisplayStatus = async () => {
    if (isProcessing) return;
    isProcessing = true;

    try {
      const tableContainer = document.getElementById('listviewtablescroll');
      if (!tableContainer) {
        return;
      }
      // 等待表格组件出现
      const tableComponent = document.querySelector('crux-table-component ');
      if (!tableComponent) {
        isProcessing = false; // Reset a flag if component not found
        return;
      }

      // 监控  tableContainer 的 class 是否发生变化，如果发生变化，说明有翻页的行为，需要清除 mounted ids
      const tableContainerObserver = new MutationObserver(() => {
        mountedAccountIds = {};
      });
      tableContainerObserver.observe(tableContainer, {
        childList: false,
        subtree: false,
        attributeFilter: ['class'],
      });
      // Process the initial state of the table immediately
      await processTable(tableComponent);
    } catch (error) {
      console.error('Error in injectDisplayStatus:', error); // Updated error message source
    }
  };

  injectDisplayStatus();
};

// listen to filter form values change
const listenFilterFormValuesChange = async (ctx: ContentScriptContext) => {
  // 监控 form id=customViewSmartFilters 下所有的 input type="checkbox" 的状态
  await waitForElement('crm-custom-outer-box');
  const formElmsContents = document.getElementsByTagName('crm-custom-outer-box')[0];
  if (formElmsContents) {
    const obs = new MutationObserver(() => {
      accountStatusUIMap.clear();
      renderStatusUIInList(ctx);
    });

    obs.observe(formElmsContents, {
      childList: false,
      subtree: false,
      attributeFilter: ['applied_filters'],
    });
  }
};

export const injectAccountsListUi = async (ctx: ContentScriptContext) => {
  listenFilterFormValuesChange(ctx);
  delay(5000).then(() => renderStatusUIInList(ctx));
  renderBatchDetectUi(ctx);
};
