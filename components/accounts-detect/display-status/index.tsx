import { StyleProvider } from '@ant-design/cssinjs';
import { InfoCircleOutlined } from '@ant-design/icons';
import { Descriptions, DescriptionsProps, Popover, Typography } from 'antd';
import { last } from 'es-toolkit';
import ReactDOM from 'react-dom/client';
import { ContentScriptContext } from 'wxt/utils/content-script-context';

import { displayTextMap } from '@/components/detect-contacts/constants';
import { ResultDataItem } from '@/components/detect-contacts/data';
import { formatMessage } from '@/components/detect-contacts/utils.ts';
import { accountListPattern } from '@/entrypoints/content';

import { getAccountNameCellInfo } from '../index';

export interface MountUIProps {
  accountId: string;
  data: ResultDataItem;
}

// Replace singleton with a map of UIs by accountId
export const accountStatusUIMap = new Map<
  string,
  {
    ui: any;
    updateProps: ((props: MountUIProps) => void) | null;
    isMounted: boolean;
  }
>();

export const getInjectContent = (data?: ResultDataItem) => {
  if (!data) {
    return '';
  }

  const { status, completed_at, events = [] } = data;
  const lastEvent = last(events);

  const currentDisplayText =
    (data &&
      data?.status &&
      displayTextMap[data?.status?.toString() as keyof typeof displayTextMap]) ||
    '';
  const currentDisplayTitle = (lastEvent && lastEvent?.name) || '';

  if (['success', 'completed', '500', 'failed', 'error'].includes(status?.toString())) {
    const items: DescriptionsProps['items'] = [
      {
        label: 'Status',
        children: displayTextMap[status as keyof typeof displayTextMap],
      },
      {
        label: 'Time',
        children: completed_at ? new Date(completed_at).toLocaleString() : '',
      },
      {
        label: 'Info',
        children: formatMessage(status, lastEvent?.name || '', data?.result),
      },
    ];
    return (
      <Popover
        title={
          <Descriptions
            title='Last Detect Info'
            items={items}
            column={1}
            style={{
              width: 320,
            }}
          />
        }
        destroyOnHidden={true} // 改为 true 以避免残留
        placement='rightTop'
        getPopupContainer={(node) => node.parentElement!}
        styles={{
          root: {
            position: 'fixed',
            zIndex: 99999,
          },
          body: {
            backgroundColor: '#fff',
            boxShadow: '0 3px 6px -4px rgba(0,0,0,.12), 0 6px 16px 0 rgba(0,0,0,.08)',
            borderRadius: '4px',
            padding: '12px',
          },
        }}
        align={{
          offset: [10, 0], // 微调位置
        }}
      >
        <InfoCircleOutlined
          style={{
            fontSize: 10,
            cursor: 'pointer',
            overflow: 'hidden',
            textOverflow: 'ellipsis',
            whiteSpace: 'nowrap',
            display: 'block',
            textAlign: 'right',
            color: ['500', 'failed', 'error'].includes(status?.toString()) ? '#faad14' : 'inherit',
          }}
        />
      </Popover>
    );
  } else {
    return (
      <Popover
        title={
          <div
            style={{
              width: 320,
              display: 'inline-block',
            }}
          >
            {currentDisplayTitle}
          </div>
        }
        placement='rightTop'
        getPopupContainer={(node) => node.parentElement!}
        destroyOnHidden={true} // 改为 true 以避免残留
        styles={{
          root: {
            position: 'fixed',
            zIndex: 99999,
          },
          body: {
            backgroundColor: '#fff',
            boxShadow: '0 3px 6px -4px rgba(0,0,0,.12), 0 6px 16px 0 rgba(0,0,0,.08)',
            borderRadius: '4px',
            padding: '12px',
          },
        }}
        align={{
          offset: [10, 0], // 微调位置
        }}
      >
        <Typography.Text
          style={{
            fontSize: 10,
            maxWidth: 120,
            opacity: 0.68,
            cursor: 'pointer',
            overflow: 'hidden',
            textOverflow: 'ellipsis',
            whiteSpace: 'nowrap',
            display: 'block',
            textAlign: 'right',
          }}
        >
          {currentDisplayText}
        </Typography.Text>
      </Popover>
    );
  }
};

export const createDisplayStatusUI = async (
  ctx: ContentScriptContext,
  { accountId, data }: MountUIProps,
) => {
  // 如果已经挂载，则更新 props
  const existingUI = accountStatusUIMap.get(accountId);
  if (existingUI?.isMounted && existingUI.updateProps) {
    existingUI.updateProps({
      accountId,
      data,
    });
    return existingUI.ui;
  }

  // 找包含 Account Name 的 lyte-exptable-td 元素
  // 提取账户信息
  const { cell: anchorElement } = getAccountNameCellInfo(accountId);
  if (!anchorElement) {
    throw new Error("Can't find Account Name Cell");
  }
  // const anchorRect = anchorElement.getBoundingClientRect();
  // const paddingLeft = parseFloat(window.getComputedStyle(anchorElement).paddingLeft);
  // const paddingRight = parseFloat(window.getComputedStyle(anchorElement).paddingRight);

  // 获取到 anchorElement 的除去padding的宽度
  // const anchorContentWidth = anchorRect.width - paddingLeft - paddingRight - 4 - 8;
  // 获取 子元素 lyte-yield 的宽度
  // const yieldElementWidth = anchorElement?.querySelector('lyte-yield')?.getBoundingClientRect()?.width ?? 0;
  // 计算剩余的宽度
  // const remainingWidth = anchorContentWidth - yieldElementWidth;

  try {
    // Verify ctx is defined before using it
    if (!ctx) {
      console.error('ContentScriptContext is required');
    }

    // Create a container for the React component
    let reactRoot: ReactDOM.Root | null = null;

    const ui = createShadowRootUi(ctx, {
      name: `inhand-accounts-detect-status-ui-${accountId}`,
      position: 'inline' as const, // Type assertion to fix TypeScript error
      append: 'last' as const,
      anchor: anchorElement,
      isolateEvents: true,
      mode: 'open',
      onMount: (
        container: HTMLElement,
        shadow: ShadowRoot,
        shadowHost: HTMLElement,
      ): HTMLElement => {
        try {
          // Use shadowHost directly instead of trying to get it from container
          const hostElement = shadowHost;
          if (!hostElement) {
            console.error('hostElement is required');
          }
          //   hostElement.style.cssText = `
          //   display: inline-block;
          //   position: static; /* 改为 static 以适应表格布局 */
          //   max-width: ${remainingWidth}px;
          //   min-width: 20px;
          //   margin-left: 6px;
          //   vertical-align: bottom;
          //   overflow: visible;
          //   float: right;
          //   margin-top: 3px;
          // `;
          hostElement.style.cssText = `
            display: inline-block;
            position: relative;
            width: max-content;
            float: right;
            margin-top: -15px;
          `;
          hostElement.classList.add('inhand-injected-element');
          hostElement.setAttribute('data-inhand-injected', 'true');
          hostElement.id = `inhand-accounts-detect-status-host-${accountId}`;

          // 添加 Shadow DOM 样式
          const style = document.createElement('style');
          style.textContent = `
            :host {
              position: static;
              display: inline-block;
              overflow: visible !important;
            }
            .ant-popover {
              position: fixed !important; /* 使用 fixed 定位 */
              z-index: 99999 !important; /* 提高层级 */
            }
            /* 调整 Popover 容器样式 */
            .ant-popover-content {
              transform-origin: center top;
              position: absolute;
              top: 100%;
              left: 0;
            }
            /* 确保内容不被截断 */
            .ant-popover-inner {
              max-width: none;
              width: auto;
            }
            /* 移除可能影响布局的边距 */
            .ant-popover-inner-content {
              padding: 12px;
              white-space: normal;
            }
            /* 确保内容不被截断 */
            .ant-popover-inner {
              max-width: none;
              width: auto;
            }
            /* 移除可能影响布局的边距 */
            .ant-popover-inner-content {
              padding: 12px;
              white-space: normal;
            }
            /* 箭头样式调整 */
            .ant-popover-arrow {
              position: absolute;
              z-index: 1;
            }
          `;
          shadow.querySelector('head')?.appendChild(style);

          const cssContainer = shadow.querySelector('head')!;
          // Create the React root
          reactRoot = ReactDOM.createRoot(container);

          // 创建一个函数来更新props
          const updateProps = (newProps: MountUIProps) => {
            if (reactRoot) {
              // handle data
              const currentData = newProps?.data;

              if (currentData) {
                hostElement.style.display = 'inline-block';
              } else {
                hostElement.style.display = 'none';
              }

              // Render the status
              reactRoot.render(
                <StyleProvider container={cssContainer}>
                  {getInjectContent(currentData)}
                </StyleProvider>,
              );
            }
          };

          // Store the UI info in the map
          accountStatusUIMap.set(accountId, {
            ui,
            updateProps,
            isMounted: true,
          });

          updateProps({ data, accountId });
        } catch (error) {
          console.error(`onMount of ${accountId} failed`, error);
        }
        return container;
      },
      onRemove: (mounted: HTMLElement | undefined): void => {
        console.log('onRemove', accountId, mounted);
        if (mounted) {
          if (reactRoot) {
            reactRoot.unmount();
            reactRoot = null;
          } else {
            ReactDOM.createRoot(mounted).unmount();
          }

          // Reset UI state in the map
          accountStatusUIMap.delete(accountId);
        }
      },
    });

    // Initialize entry in a map if not exists
    if (!accountStatusUIMap.has(accountId)) {
      accountStatusUIMap.set(accountId, {
        ui,
        updateProps: null,
        isMounted: false,
      });
    }

    return ui;
  } catch (error) {
    console.error('Error creating shadow root UI:', error);
    throw error;
  }
};

export const mountDisplayStatusUI = async ({
  ctx,
  accountId,
  data,
}: MountUIProps & { ctx: ContentScriptContext }) => {
  try {
    // 找到 id=accountId 的 lyte-exptable-tr 元素
    const tableRow = document.getElementById(accountId);
    const currentUrl = window.location.origin + window.location.pathname;
    const isOnListPage = accountListPattern.includes(currentUrl);
    if (!isOnListPage) {
      // delete all accountStatusUIMap
      accountStatusUIMap.clear();
      return;
    }

    if (!tableRow) {
      console.error(`Table row with ID ${accountId} not found`);
      // If the row is not found, ensure the entry is removed from the map
      // in case it exists from a previous state but the DOM element is gone.
      accountStatusUIMap.delete(accountId);
      return;
    }

    // Create a new UI or get existing one if createDisplayStatusUI handles updates
    const ui = await createDisplayStatusUI(ctx, {
      accountId,
      data,
    });

    // Always mount if the UI exists and not already mounted for this specific accountId
    const accountUI = accountStatusUIMap.get(accountId);
    if (ui && (!accountUI || !accountUI.isMounted)) {
      ui.mount();
    }
    return ui;
  } catch (error) {
    console.error(`Error mounting UI for account ${accountId}:`, error);
  }
};
