import { DownOutlined, InfoCircleOutlined } from '@ant-design/icons';
import { useRequest } from 'ahooks';
import { Button, Dropdown, Popover, Space } from 'antd';
import React, { useState } from 'react';

import { AccountInfoItem } from '@/components/accounts-detect/data';
import CustomPromptModalModal, { CustomPromptsType } from '@/components/custom-prompt-modal';

import MagicSVG from '~/assets/magic.svg';

import ResultModal from '../result-modal';

import { batchProcessAccounts, BatchProcessResult } from './batch-server';

import './index.css';

interface BatchButtonProps {
  onUpdateStatus: (accountId: string, status: any) => void;
  selectedAccountInfo: Record<string, AccountInfoItem>;
}

const BatchButton: React.FC<BatchButtonProps> = ({ selectedAccountInfo, onUpdateStatus }) => {
  const [customPromptModalOpen, setCustomPromptModalOpen] = useState<boolean>(false);
  const [allDetectProcessData, setAllDetectProcessData] = useState<BatchProcessResult[]>();
  const [resultModalOpen, setResultModalOpen] = useState<boolean>(false);
  const spaceRef = useRef(null);

  const { run: handleBatchDetect, loading } = useRequest(
    async (customPrompts?: CustomPromptsType) => {
      setResultModalOpen(false);
      setAllDetectProcessData([]);
      return await batchProcessAccounts({
        selectedAccountInfo, // 所有账户信息的映射
        customPrompts, // 自定义提示
        onAccountProgress: (accountId, process) => {
          // 转换 BatchProcessResult 为 ResultDataItem 格式
          const convertedData = {
            _id: process.taskId,
            status: process.status,
            type: 'sales_agent',
            completed_at: ['completed', 'failed'].includes(process.status) ? process.ts : undefined,
            created_at: process.ts,
            started_at: process.ts,
            updated_at: process.ts,
            tags: {
              account_id: accountId,
              task_type: 'sales_agent',
            },
            events: [
              {
                status: process.status,
                type: process.type || 'start',
                name: process.message || 'Processing',
                ts: process.ts || new Date().toISOString(),
                details: {
                  source: [],
                  data: process.data || [],
                  total: process.contactCount || 0,
                },
              },
            ],
            result: process.error
              ? undefined
              : {
                  total: process.contactCount || 0,
                },
            error: process.error,
          };

          onUpdateStatus(accountId, convertedData);
        },
        onComplete: (results) => {
          setAllDetectProcessData(results);
          setResultModalOpen(true);
        },
      });
    },
    {
      manual: true,
      refreshDeps: [selectedAccountInfo],
    },
  );

  const handleBatchDetectWithCustomPrompts = (customPrompts?: CustomPromptsType) => {
    handleBatchDetect(customPrompts);
    setCustomPromptModalOpen(false);
  };

  const failedAccounts = useMemo(
    () => allDetectProcessData?.filter((item) => item.status === 'failed'),
    [allDetectProcessData],
  );

  return (
    <Space id='detect-contacts-container' ref={spaceRef}>
      {failedAccounts && failedAccounts?.length > 0 && (
        <Popover
          title={failedAccounts && failedAccounts?.length > 0 ? 'Failed Accounts:' : undefined}
          content={
            failedAccounts && failedAccounts?.length > 0 ? (
              <Space direction='vertical'>
                {failedAccounts.map((v, index) => (
                  // eslint-disable-next-line react/no-array-index-key
                  <span key={`show-name-${index}`}>{v.name}</span>
                ))}
              </Space>
            ) : undefined
          }
          placement='bottom'
          getPopupContainer={(node) => node.parentElement as HTMLElement}
        >
          <InfoCircleOutlined
            style={{
              // fontSize: 10,
              cursor: 'pointer',
              overflow: 'hidden',
              textOverflow: 'ellipsis',
              whiteSpace: 'nowrap',
              display: 'block',
              textAlign: 'right',
              color: '#faad14',
            }}
          />
        </Popover>
      )}
      <Dropdown
        disabled={loading || (selectedAccountInfo && Object.keys(selectedAccountInfo).length === 0)}
        getPopupContainer={(node) => node.parentElement as HTMLElement}
        menu={{
          items: [
            {
              key: 'standard',
              onClick: () => handleBatchDetect(),
              label: 'Use Standard Detect',
              disabled: loading,
            },
            {
              key: 'custom',
              disabled: loading,
              onClick: () => setCustomPromptModalOpen(true),
              label: 'Use Custom AI Prompt',
            },
          ],
        }}
        overlayStyle={{
          width: 180,
        }}
      >
        <Button
          icon={
            <img
              src={MagicSVG}
              alt='magic'
              width={20}
              style={{
                color: '#fff',
              }}
            />
          }
          disabled={
            loading || (selectedAccountInfo && Object.keys(selectedAccountInfo).length === 0)
          }
          loading={loading}
          onClick={(e) => e.preventDefault()}
          className='batch-button'
        >
          <Space>
            Detect Contacts <DownOutlined />
          </Space>
        </Button>
      </Dropdown>
      {customPromptModalOpen && (
        <CustomPromptModalModal
          open={customPromptModalOpen}
          onConfirm={(data: CustomPromptsType) => {
            handleBatchDetectWithCustomPrompts(data);
          }}
          onCancel={() => setCustomPromptModalOpen(false)}
        />
      )}
      {resultModalOpen && allDetectProcessData && (
        <ResultModal
          containerElement={
            (spaceRef?.current ??
              document.getElementById('detect-contacts-container')) as HTMLElement
          }
          open={resultModalOpen}
          data={allDetectProcessData}
          failedAccounts={failedAccounts}
          onCancel={() => setResultModalOpen(false)}
          onOk={() => setResultModalOpen(false)}
        />
      )}
    </Space>
  );
};

export default BatchButton;
