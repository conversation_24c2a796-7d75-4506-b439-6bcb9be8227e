import { EventSourceMessage, fetchEventSource } from '@microsoft/fetch-event-source';
import { isEmpty } from 'es-toolkit/compat';

import { AccountInfoItem } from '@/components/accounts-detect/data';
import { CustomPromptsType } from '@/components/custom-prompt-modal';
import { EventStreamItem, TaskStatusType } from '@/components/detect-contacts/data';
import {
  CompanyInfoType,
  detectTaskStatus,
  startDetectTask,
} from '@/components/detect-contacts/service';
import config from '@/config';
import { BASE_URL, X_API_KEY } from '@/utils/request';

/**
 * 批量处理结果类型
 */
export interface BatchProcessResult extends EventStreamItem {
  accountId: string;
  name?: string;
  status: TaskStatusType;
  taskId?: string;
  error?: any;
  contactCount?: number;
  [key: string]: any;
}

/**
 * 批量处理参数类型
 */
export interface BatchProcessParams {
  selectedAccountInfo: Record<string, AccountInfoItem>;
  customPrompts?: CustomPromptsType;
  onComplete?: (results: BatchProcessResult[]) => void;
  onAccountProgress?: (accountId: string, result: BatchProcessResult) => void;
}

/**
 * 启动EventSource连接监听会话事件
 */
export const startBatchEventSource = async (
  taskId: string,
  accountId: string,
  accountName: string | undefined,
  results: BatchProcessResult[],
  onAccountProgress?: (accountId: string, result: BatchProcessResult) => void,
): Promise<BatchProcessResult> => {
  // 创建新的AbortController控制请求
  const ctrl = new AbortController();

  // 获取当前账户在结果数组中的索引
  const currentIndex = results.findIndex((r) => r.accountId === accountId);

  // 构建EventSource URL
  const url = config.isLocal
    ? `${BASE_URL}/api/sales-agent/tasks/${taskId}/stream`
    : `${BASE_URL}/api/plm/common/sales-agent/tasks/${taskId}/stream`;

  let result: BatchProcessResult = {
    accountId,
    status: 'pending',
    taskId,
    name: accountName,
    type: 'start',
    message: 'Initializing',
    ts: new Date().toISOString(),
    data: [],
  };

  try {
    await fetchEventSource(url, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'x-api-key': X_API_KEY,
      },
      signal: ctrl.signal,

      onopen: async (response) => {
        if (response.ok) {
          result = {
            ...result,
            type: 'start',
            name: accountName,
            message: 'Event source opened successfully',
            ts: new Date().toISOString(),
          };

          if (currentIndex >= 0) {
            results[currentIndex] = result;
            onAccountProgress?.(accountId, result);
          }
        } else if (response.status >= 400 && response.status < 500 && response.status !== 429) {
          result = {
            ...result,
            status: 'failed',
            type: 'error',
            name: accountName,
            message: `Failed to open event source: ${response.statusText || 'Client error'}`,
            error: {
              status: response.status,
              message: response.statusText || 'Client error',
            },
            ts: new Date().toISOString(),
          };

          if (currentIndex >= 0) {
            results[currentIndex] = result;
            onAccountProgress?.(accountId, result);
          }

          ctrl.abort();
        }
      },

      onmessage: async (event: EventSourceMessage) => {
        try {
          const parsedData: EventStreamItem = JSON.parse(event.data);
          const { type, message, status } = parsedData;

          // 更新结果
          result = {
            ...result,
            status: status || 'pending',
            type,
            message,
            name: accountName,
            data: parsedData.data,
            ts: new Date().toISOString(),
          };

          // 处理完成或成功事件
          if (['completed', 'success'].includes(type)) {
            result = {
              ...result,
              status: 'completed',
              message: type === 'completed' ? 'Detection completed' : 'Task completed',
              contactCount: parsedData?.total ?? 0,
            };
          }

          // 处理失败或错误事件
          if (['failed', 'error'].includes(type)) {
            result = {
              ...result,
              name: accountName,
              status: 'failed',
              message: message || 'Detection failed',
              error: parsedData,
            };
            ctrl.abort();
          }

          // 更新主结果数组
          if (currentIndex >= 0) {
            results[currentIndex] = result;
            onAccountProgress?.(accountId, result);
          }
        } catch (parseError) {
          console.error('Failed to parse event data as JSON:', event.data, parseError);

          result = {
            ...result,
            name: accountName,
            status: 'failed',
            type: 'error',
            message: 'Failed to parse event data',
            error: { parseError: String(parseError), rawData: event.data },
            ts: new Date().toISOString(),
          };

          if (currentIndex >= 0) {
            results[currentIndex] = result;
            onAccountProgress?.(accountId, result);
          }

          ctrl.abort();
        }
      },

      onclose: async () => {
        console.log(`EventSource connection closed for account ${accountId}`);

        // 如果连接正常关闭但状态不是已完成，标记为失败
        if (result.status !== 'completed' && result.status !== 'failed') {
          result = {
            ...result,
            status: 'failed',
            type: 'error',
            name: accountName,
            message: 'Event source closed unexpectedly',
            ts: new Date().toISOString(),
          };

          if (currentIndex >= 0) {
            results[currentIndex] = result;
            onAccountProgress?.(accountId, result);
          }
        }
      },

      onerror: (err) => {
        console.error(`EventSource failed for account ${accountId}:`, err);

        result = {
          ...result,
          status: 'failed',
          type: 'error',
          name: accountName,
          message: `EventSource error: ${(err as Error).message || 'Unknown error'}`,
          error: err,
          ts: new Date().toISOString(),
        };

        if (currentIndex >= 0) {
          results[currentIndex] = result;
          onAccountProgress?.(accountId, result);
        }

        ctrl.abort();
      },
    });
  } catch (error: any) {
    console.error(`Error in batch event source for account ${accountId}:`, error);

    result = {
      ...result,
      name: accountName,
      status: 'failed',
      type: 'error',
      message: `Error in event source: ${error.message || 'Unknown error'}`,
      error,
      ts: new Date().toISOString(),
    };

    if (currentIndex >= 0) {
      results[currentIndex] = result;
      onAccountProgress?.(accountId, result);
    }
  }

  return result;
};

/**
 * 批量处理账户
 * @param params 批量处理参数
 * @returns 批量处理结果数组Promise
 */
export const batchProcessAccounts = async ({
  selectedAccountInfo,
  onComplete,
  onAccountProgress,
  customPrompts,
}: BatchProcessParams): Promise<BatchProcessResult[]> => {
  if (!selectedAccountInfo || isEmpty(selectedAccountInfo)) {
    return [];
  }

  const accountIds = Object.keys(selectedAccountInfo);

  // 初始化所有账户的处理结果为pending状态
  const results: BatchProcessResult[] = accountIds.map((accountId) => ({
    accountId,
    name: selectedAccountInfo[accountId].name,
    status: 'pending',
    type: 'start',
    message: 'Waiting to start',
    ts: new Date().toISOString(),
    data: [],
  }));

  // 并发处理每个账户，最大并发数为100
  const concurrentLimit = 100;
  const queue = accountIds;
  const activePromises: Promise<void>[] = [];

  while (queue.length > 0 || activePromises.length > 0) {
    // 添加新的处理任务，直到达到并发限制或队列为空
    while (activePromises.length < concurrentLimit && queue.length > 0) {
      const accountId = queue.shift()!;
      const accountInfo = selectedAccountInfo[accountId];

      activePromises.push(
        (async () => {
          try {
            const index = results.findIndex((r) => r.accountId === accountId);
            if (index >= 0) {
              results[index] = {
                ...results[index],
                name: accountInfo.name,
                status: 'pending',
                type: 'start',
                message: 'Querying task status...',
                ts: new Date().toISOString(),
              };
              onAccountProgress?.(accountId, results[index]);
            }

            const { _id: taskId, result } = await detectTaskStatus(accountId);
            const { status = 'unknown' } = result ?? {};
            console.log('status', status);
            console.log('result', result);

            if (taskId && ['running', 'pending', 'queued'].includes(status)) {
              results[index] = {
                ...results[index],
                taskId,
                name: accountInfo.name,
                type: 'start',
                message: 'Reconnecting to existing task...',
                ts: new Date().toISOString(),
              };
              onAccountProgress?.(accountId, results[index]);

              await startBatchEventSource(
                taskId,
                accountId,
                accountInfo?.name,
                results,
                onAccountProgress,
              );
            } else if (
              [
                'not_found',
                'unknown',
                'error',
                'failed',
                'completed',
                'canceled',
                'success',
              ].includes(status)
            ) {
              // 获取公司信息
              const companyInfo: CompanyInfoType = {
                account_id: accountId,
                custom_prompts: customPrompts,
              };

              results[index] = {
                status: 'pending',
                accountId,
                name: accountInfo.name,
                type: 'start',
                message: 'Starting new detection task...',
                ts: new Date().toISOString(),
              };
              onAccountProgress?.(accountId, results[index]);

              // 启动检测任务
              const { task_id } = await startDetectTask(companyInfo);

              if (task_id) {
                // 启动事件源监听
                await startBatchEventSource(
                  task_id,
                  accountId,
                  accountInfo?.name,
                  results,
                  onAccountProgress,
                );
              } else {
                results[index] = {
                  ...results[index],
                  taskId: task_id,
                  name: accountInfo.name,
                  status: 'failed',
                  type: 'error',
                  message: 'Failed to initialize detection task',
                  ts: new Date().toISOString(),
                };
                onAccountProgress?.(accountId, results[index]);
              }
            }
          } catch (error: any) {
            console.error(`Error processing account ${accountId}:`, error);

            const index = results.findIndex((r) => r.accountId === accountId);
            if (index >= 0) {
              results[index] = {
                ...results[index],
                name: accountInfo.name,
                status: 'failed',
                type: 'error',
                message: `Error: ${error.message || 'Unknown error'}`,
                error,
                ts: new Date().toISOString(),
              };
              onAccountProgress?.(accountId, results[index]);
            }
          }
        })(),
      );
    }

    // 等待任何一个处理完成
    if (activePromises.length > 0) {
      await Promise.race(
        activePromises.map((p, i) =>
          p.then(() => {
            // 从活动Promise数组中移除已完成的Promise
            activePromises.splice(i, 1);
          }),
        ),
      );
    }
  }

  // 检查是否所有任务都已完成（completed 或 failed）
  const allTasksFinished = results.every((result) =>
    ['completed', 'failed'].includes(result.status),
  );

  // 只有当所有任务都完成时才触发完成回调
  if (allTasksFinished) {
    onComplete?.(results);
  }

  return results;
};
