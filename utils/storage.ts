/* global chrome */

/**
 * Utilities for interacting with chrome.storage.local for operation history.
 */

const HISTORY_KEY = 'inhand_operation_history';
const MAX_HISTORY_SIZE = 50; // Store the last 50 records

/**
 * Interface for a single operation record.
 */
export interface OperationRecord {
  timestamp: number; // Unix timestamp (ms)
  accountId: string;
  accountName?: string; // Optional, if available
  status: 'success' | 'failed'; // Simplified status
  message: string; // User-friendly message
  contactCount?: number; // Number of contacts detected on success
}

/**
 * Retrieves the operation history.
 * @returns A promise that resolves with the history array or an empty array if not found/error.
 */
export const getOperationHistory = (): Promise<OperationRecord[]> => {
  return new Promise((resolve) => {
    chrome.storage.local.get([HISTORY_KEY], (result) => {
      if (chrome.runtime.lastError) {
        console.error('Error getting operation history:', chrome.runtime.lastError);
        return resolve([]); // Return empty array on error
      }
      const history = result[HISTORY_KEY];
      if (Array.isArray(history)) {
        resolve(history);
      } else {
        resolve([]); // Return empty array if data is invalid or not found
      }
    });
  });
};

/**
 * Adds a new record to the operation history.
 * Keeps the history size limited to MAX_HISTORY_SIZE.
 * @param record - The OperationRecord to add.
 * @returns A promise that resolves when the record is added.
 */
export const addOperationRecord = async (record: OperationRecord): Promise<void> => {
  try {
    const currentHistory = await getOperationHistory();
    // Prepend the new record
    const newHistory = [record, ...currentHistory];
    // Trim the history if it exceeds the max size
    if (newHistory.length > MAX_HISTORY_SIZE) {
      newHistory.length = MAX_HISTORY_SIZE;
    }
    // Save the updated history
    await new Promise<void>((resolve, reject) => {
      chrome.storage.local.set({ [HISTORY_KEY]: newHistory }, () => {
        if (chrome.runtime.lastError) {
          console.error('Error saving operation history:', chrome.runtime.lastError);
          return reject(chrome.runtime.lastError);
        }
        resolve();
      });
    });
  } catch (error) {
    // Log the error but don't prevent the original operation
    console.error('Failed to add operation record:', error);
    // We might re-throw or handle this differently depending on requirements
    // For now, just log it.
  }
};

/**
 * Clears the entire operation history.
 * @returns A promise that resolves when the history is cleared.
 */
export const clearOperationHistory = (): Promise<void> => {
  return new Promise((resolve, reject) => {
    chrome.storage.local.remove([HISTORY_KEY], () => {
      if (chrome.runtime.lastError) {
        console.error('Error clearing operation history:', chrome.runtime.lastError);
        return reject(chrome.runtime.lastError);
      }
      resolve();
    });
  });
};
