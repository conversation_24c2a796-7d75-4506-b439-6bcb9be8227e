import axios, { AxiosRequestConfig } from 'axios';

import CONFIG from '../config';

export const BASE_URL = CONFIG.apiBaseUrl;

// 国内：https://poweris.inhand.online ；487ee623cd184a299dc1c351b102edeb
// 海外：https://poweris.inhandnetworks.com ；0ecf03710f684591a9aed0d9a16401de
export const X_API_KEY = '0ecf03710f684591a9aed0d9a16401de';

const instance = axios.create({
  baseURL: BASE_URL,
  withCredentials: false, // Set too false to avoid CORS preflight issues
  validateStatus: (status) => {
    return status >= 200 && status < 500;
  },
});

instance.interceptors.request.use(async (config) => {
  config.headers['x-api-key'] = X_API_KEY;
  config.headers['Content-Type'] = 'application/json';
  config.headers['Accept'] = 'application/json';
  config.headers['Access-Control-Allow-Origin'] = '*';
  return config;
});

const request = async (url: string, config: AxiosRequestConfig) => {
  let data: any;
  if (config && config?.params) {
    const { params } = config;
    data = Object.fromEntries(Object.entries(params).filter(([, value]) => value !== ''));
  }

  try {
    const response = await instance(url, {
      ...config,
      params: data,
    });
    return response.data;
  } catch (error) {
    // Add better error handling
    if (axios.isAxiosError(error)) {
      if (error.response?.status === 403) {
        throw new Error('API key is invalid or expired');
      }
      throw new Error(`Request failed: ${error.response?.data?.message || error.message}`);
    }
    throw error;
  }
};

export default request;
