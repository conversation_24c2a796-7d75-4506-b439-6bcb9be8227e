// @ts-ignore
import { storage } from '#imports';

// Define the token storage structure
export interface TokenConfig {
  token: {
    accessToken: string;
    refreshToken: string;
    expiresIn: number;
    timeStamp: number;
  };
  config: {
    isLogin: boolean;
    apiDomain: string;
    clientId: string;
    clientSecret: string;
  };
}

// Create type-safe storage
export const tokenStorage = storage<TokenConfig>('token');

/**
 * Fetch Zoho configuration from storage
 */
export const fetchZohoConfig = async (): Promise<TokenConfig> => {
  try {
    // Get the token and config from storage
    const token = (await tokenStorage.getItem('token')) || null;
    const config = (await tokenStorage.getItem('config')) || null;

    return { token, config } as TokenConfig;
  } catch (error) {
    console.error('Error fetching Zoho config:', error);
    return { token: null, config: null } as unknown as TokenConfig;
  }
};
