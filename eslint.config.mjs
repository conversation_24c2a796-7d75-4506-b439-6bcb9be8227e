import babelPlugin from '@babel/eslint-plugin';
import js from '@eslint/js';
import eslintConfigPrettier from 'eslint-config-prettier';
import importPlugin from 'eslint-plugin-import';
import jestPlugin from 'eslint-plugin-jest';
import reactPlugin from 'eslint-plugin-react';
import reactHooksPlugin from 'eslint-plugin-react-hooks';
import youMightNotNeedAnEffect from 'eslint-plugin-react-you-might-not-need-an-effect';
import unicornPlugin from 'eslint-plugin-unicorn';
import globals from 'globals';
import tseslint from 'typescript-eslint';

import autoImports from './.wxt/eslint-auto-imports.mjs';
// No longer using FlatCompat

// 创建最终配置
export default [
  // 自动导入配置
  autoImports,

  // 全局忽略 - 必须放在前面
  {
    ignores: [
      'node_modules/**',
      'dist/**',
      'build/**',
      'public/**',
      '.history/**',
      '.wxt/**',
      '.output/**',
      '.idea/**',
      '.vscode/**',
      '.git/**',
      '.DS_Store',
      '*.log',
      '*.tmp',
      '.cursor/**',
      'coverage/**',
      '.next/**',
      '.vercel/**',
      '*.svg',
      '**/*.svg',
    ],
  },

  // 基础 JS 推荐配置
  js.configs.recommended,

  // TypeScript 配置
  ...tseslint.configs.recommended,

  // React 配置
  {
    files: ['**/*.jsx', '**/*.tsx'],
    plugins: {
      react: reactPlugin,
    },
    rules: reactPlugin.configs.recommended.rules,
    settings: {
      react: {
        version: 'detect',
      },
    },
  },

  // React Hooks 配置
  {
    plugins: {
      'react-hooks': reactHooksPlugin,
    },
    rules: reactHooksPlugin.configs.recommended.rules,
  },

  // Prettier 配置（必须放在最后以覆盖其他样式规则）
  eslintConfigPrettier,

  // 全局设置（适用于所有文件）
  {
    languageOptions: {
      globals: {
        ...globals.browser,
        ...globals.node,
      },
    },
  },

  // TypeScript 特定设置
  {
    files: ['**/*.ts', '**/*.tsx', '**/*.d.ts'],
    languageOptions: {
      parserOptions: {
        project: './tsconfig.json',
      },
    },
  },

  // 自定义规则
  {
    plugins: {
      react: reactPlugin,
      jest: jestPlugin,
      '@babel': babelPlugin,
      unicorn: unicornPlugin,
      'react-hooks': reactHooksPlugin,
      import: importPlugin,
    },
    rules: {
      strict: ['error', 'never'],
      '@babel/new-cap': 0,
      '@babel/no-invalid-this': 0,
      '@babel/no-unused-expressions': 2,
      '@babel/object-curly-spacing': 0,
      '@babel/semi': 2,
      'react/display-name': 0,
      'react/jsx-props-no-spreading': 0,
      'react/state-in-constructor': 0,
      'react/static-property-placement': 0,
      'react/destructuring-assignment': 'off',
      'react/jsx-filename-extension': 'off',
      'react/no-array-index-key': 'warn',
      'react-hooks/rules-of-hooks': 'error',
      'react-hooks/exhaustive-deps': 'warn',
      'react/require-default-props': 0,
      'react/jsx-fragments': 0,
      'react/jsx-wrap-multilines': 0,
      'react/forbid-prop-types': 0,
      'react/sort-comp': 0,
      'react/jsx-one-expression-per-line': 0,
      'generator-star-spacing': 0,
      'function-paren-newline': 0,
      'sort-imports': 0,
      'class-methods-use-this': 0,
      'no-confusing-arrow': 0,
      'linebreak-style': 0,
      'no-prototype-builtins': 'off',
      'unicorn/prevent-abbreviations': 'off',
      'arrow-body-style': 0,
      'arrow-parens': 0,
      'object-curly-newline': 0,
      'implicit-arrow-linebreak': 0,
      'operator-linebreak': 0,
      'no-param-reassign': 2,
      'space-before-function-paren': 0,
      'react/self-closing-comp': 1,
      'react/jsx-key': 1,
      'react/react-in-jsx-scope': 'off',
      'react/prop-types': 'off',
      'no-unused-vars': 'off',
      quotes: ['error', 'single', { avoidEscape: true }],
      'jsx-quotes': ['error', 'prefer-single'],
      // 导入排序规则
      'import/order': [
        'error',
        {
          groups: [
            'builtin', // Node.js 内置模块
            'external', // 来自 node_modules 的模块
            'internal', // 来自 @ 或 ~ 等别名的模块
            'parent', // 父目录导入
            'sibling', // 同级目录导入
            'index', // 当前目录导入
          ],
          pathGroups: [
            {
              pattern: '#*/**',
              group: 'internal',
              position: 'after',
            },
            {
              pattern: '@*/**',
              group: 'internal',
              position: 'after',
            },
            {
              pattern: '~*/**',
              group: 'internal',
              position: 'after',
            },
          ],
          alphabetize: {
            order: 'asc', // 按字母顺序排序
            caseInsensitive: true, // 忽略大小写
          },
          'newlines-between': 'always', // 不同组之间添加空行
        },
      ],
    },
    settings: {
      'import/resolver': {
        node: {
          extensions: ['.js', '.jsx', '.ts', '.tsx', '.d.ts'],
        },
      },
      'import/parsers': {
        '@typescript-eslint/parser': ['.ts', '.tsx', '.d.ts'],
      },
      'import/extensions': ['.js', '.mjs', '.jsx', '.ts', '.tsx', '.d.ts'],
      'import/external-module-folders': ['node_modules', 'node_modules/@types'],
      polyfills: ['fetch', 'Promise', 'URL', 'object-assign'],
      react: {
        version: 'detect',
      },
    },
  },

  // TypeScript 文件特定配置 - 关闭一些可能冲突的规则
  {
    files: ['**/*.{ts,tsx}'],
    rules: {
      'no-undef': 'off',
      'no-unused-vars': 'off',
      '@typescript-eslint/no-explicit-any': 'off',
      '@typescript-eslint/triple-slash-reference': 'off',
      '@typescript-eslint/ban-ts-comment': 'off',
      'prefer-const': 'off',
    },
  },

  // CSS 文件配置 - 忽略 CSS 文件
  {
    files: ['**/*.css'],
    ignores: ['**/*.css'],
  },

  {
    files: ['**/*.{js,jsx}'],
    plugins: {
      'react-you-might-not-need-an-effect': youMightNotNeedAnEffect,
    },
    rules: {
      'react-you-might-not-need-an-effect/you-might-not-need-an-effect': 'warn',
    },
  },
];
