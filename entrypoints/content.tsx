import { injectAccountsListUi } from '@/components/accounts-detect';
import { accountStatusUIMap } from '@/components/accounts-detect/display-status';
import { renderDetectContacts } from '@/components/detect-contacts/render.tsx';

import { defineContentScript, MatchPattern } from '#imports';

import './content-ui/style.css';

export const accountListPattern = new MatchPattern(
  '*://crm.zoho.com/crm/*/tab/Accounts/custom-view/*/list',
);
export const accountProfilePattern = new MatchPattern('*://crm.zoho.com/crm/*/tab/Accounts/*');

export default defineContentScript({
  matches: [
    '*://crm.zoho.com/crm/*/tab/Accounts/custom-view/*/list*',
    '*://crm.zoho.com/crm/*/tab/Accounts/*',
    '*://crm.zoho.com/*', // 添加更广泛的匹配，确保在所有 Zoho CRM 页面上运行
    '*://one.zoho.com/*',
    '*://one.zoho.com/zohoone/*/home/<USER>/crm/*/tab/Accounts/custom-view/*/list',
    '*://one.zoho.com/zohoone/*/home/<USER>/crm/*/tab/Accounts/*',
  ],
  runAt: 'document_end',
  cssInjectionMode: 'ui', // Enable UI mode for createShadowRootUi
  allFrames: true,

  async main(ctx) {
    document.body.setAttribute('inhand-sales-agent', 'org663291548');

    const link = document.createElement('link');
    link.rel = 'stylesheet';
    link.type = 'text/plain';
    link.href = 'https://cdn.jsdelivr.net/npm/antd@5.15.5/dist/antd.min.css'; // AntD v5 的样式
    link.crossOrigin = 'anonymous';

    // 插到页面 <head> 里
    (document.head || document.documentElement).appendChild(link);

    // 初始 URL 检查
    const checkUrl = (
      baseUrl: string,
      search?: {
        old?: string;
        new?: string;
      },
    ) => {
      if (accountListPattern.includes(baseUrl)) {
        const { old: oldSearch, new: newSearch } = search ?? {};
        if (newSearch && oldSearch !== newSearch) {
          accountStatusUIMap.clear();
        }
        injectAccountsListUi(ctx);
      } else if (accountProfilePattern.includes(baseUrl)) {
        accountStatusUIMap.clear();
        renderDetectContacts(ctx);
        // query inhand-batch-detect-ui tag and set display to none
        const inhandBatchDetectUi = document.getElementsByTagName('inhand-batch-detect-ui')?.[0];

        if (inhandBatchDetectUi) {
          (inhandBatchDetectUi as HTMLElement).style.display = 'none';
        }
      } else {
        (window as any).inhandBatchDetectSelectedAccountInfos = {};
        (window as any).inhandBatchDetectMountUI = null;
        const inhandBatchDetectUi = document.getElementsByTagName('inhand-batch-detect-ui')?.[0];
        if (inhandBatchDetectUi) {
          (inhandBatchDetectUi as HTMLElement).style.display = 'none';
        }
      }
    };

    // 首次加载时执行检查
    checkUrl(window.location.origin + window.location.pathname);

    // 监听后续的 URL 变化
    ctx.addEventListener(window, 'wxt:locationchange', ({ newUrl, oldUrl }) => {
      if (newUrl.origin !== 'https://crm.zoho.com/_wms/pconnect.sas') {
        checkUrl(newUrl.origin + newUrl.pathname, {
          old: oldUrl.search,
          new: newUrl.search,
        });
      }
    });
  },
});
