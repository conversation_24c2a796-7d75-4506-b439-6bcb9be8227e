// 环境配置
const isDevelopment = import.meta.env.MODE === 'development';
const isProduction = import.meta.env.MODE === 'production';
console.log('config import.meta.env.MODE', import.meta.env.MODE);

export default {
  // 环境标识
  isLocal: isDevelopment,
  isDevelopment,
  isProduction,

  // API 配置
  apiBaseUrl: isDevelopment ? 'http://127.0.0.1:3000' : 'https://poweris.inhandnetworks.com',

  // 调试配置
  debug: import.meta.env.VITE_DEBUG === 'true',
  logLevel: import.meta.env.VITE_LOG_LEVEL || 'error',

  // 环境名称
  env: import.meta.env.MODE || 'production',
};
